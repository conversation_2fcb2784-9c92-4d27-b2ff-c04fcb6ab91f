package logic.crossArena;

import Message.Inner.GRCrossArena;
import Message.S2CArenaMsg;
import Message.S2CArenaMsg.RankingInfo;
import Message.S2CArenaMsg.EnemyMsg;
import Message.S2CCrossArenaMsg.CrossEnemyMsg;
import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.CrossChallengeRsp;
import Message.S2CHeroMsg;
import Message.S2CPlayerMsg;
import com.google.protobuf.ByteString;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;
import com.google.protobuf.InvalidProtocolBufferException;

import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.S2CFightMsg.FightDataRsp;
import Message.S2CFightMsg.FightDataRspID;
import Message.Inner.GRCrossArena.GRACrossChallengeRsp;
import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.S2CCrossArenaMsg.GetCrossArenaRankRsp;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CrossChallengeDataScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRACrossChallengeRsp getData = (GRACrossChallengeRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		try {
			CrossChallengeRsp.Builder builder = CrossChallengeRsp.newBuilder();
			builder.setResult(getData.getResult());

			FighterInfo selfb = FighterInfo.parseFrom(getData.getSelf());
			builder.setSelf(selfb);
			FighterInfo enemyb = FighterInfo.parseFrom(getData.getEnemy());
			builder.setEnemy(enemyb);
			builder.setArenaScore(getData.getArenaScore());
			builder.setDayChallengeNum(getData.getDayChallengeNum());
			builder.setReplayId(getData.getReplayId());
			builder.setCurRank(getData.getCurRank());
			builder.setRankHighest(getData.getRankHighest());
			if(builder.getResult()!=1)
			{
				builder.setChallengeCD(10);
			}
			else {
				builder.setChallengeCD(0);
				rewardAfterWin(player,builder);
			}

			for(ByteString byteString:getData.getEnemyListList()) {
				CrossEnemyMsg cenemyMsg = CrossEnemyMsg.parseFrom(byteString);
				EnemyMsg.Builder enemyMsg = EnemyMsg.newBuilder();
				enemyMsg.setPlayerId(cenemyMsg.getPlayerId());
				enemyMsg.setRank(cenemyMsg.getRank());
				enemyMsg.setPower(cenemyMsg.getPower());
				enemyMsg.setName(cenemyMsg.getName());
				enemyMsg.setHeroId(cenemyMsg.getHeroId());
				enemyMsg.setRobot(cenemyMsg.getRobot());
				enemyMsg.setViewCard(cenemyMsg.getViewCard());
				enemyMsg.setManifesto(cenemyMsg.getManifesto());
				builder.addEnemyList(enemyMsg);
			}
			// 推送挑战结果
			MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.CrossChallengeRspID.CrossChallengeRspMsgID_VALUE,
					builder.build().toByteArray()));

			// 战斗内容推送
			FightDataRsp.Builder fBuilder = FightDataRsp.parseFrom(getData.getFightData()).toBuilder();
			MessageUtils.send(player, player.getFactory().fetchSMessage(FightDataRspID.FightDataRspMsgID_VALUE,
					fBuilder.build().toByteArray()));

		}
		catch (Exception ex)
		{
			System.out.printf("CrossChallengeData",ex.toString());
		}



		return null;
	}
	/**
	 * 胜利后奖励
	 *
	 * @param player
	 * @param builder
	 */
	private void rewardAfterWin(Player player, CrossChallengeRsp.Builder builder) {
		// 翻牌方式
		int dropId = BeanTemplet.getGlobalBean(92).getInt_value();
		List<Item> dropItem = DropService.getDropItems(player.getDropManager(), dropId);
		t_dropBean dropBean = BeanTemplet.getDropBean(dropId);
		String[] drops = StringUtils.split(dropBean.getItem(),";");
		for (int i = 0; i < drops.length; i++) {
			if (drops[i].contains(dropItem.get(0).getId() + "," + dropItem.get(0).getNum())) {
				builder.setDropIndex(i);
				break;
			}
		}
		// 奖励道具
		List<Item> items = new ArrayList<>();
		items.addAll(BeanFactory.createProps(-1, player.getLevel() * 24));// 金币
		items.addAll(BeanFactory.createProps(-6, 0));// 精灵经验
		items.addAll(BeanFactory.createProps(-7, 0));// 队伍经验
		for (Item item : items) {
			RewardItemMsg.Builder riBuilder = RewardItemMsg.newBuilder();
			riBuilder.setItemId(item.getId());
			riBuilder.setItemNum(item.getNum());
			builder.addItemList(riBuilder);
		}
		items.add(dropItem.get(0));
		player.getBackpackManager().addItems(items, true, true, Reason.ARENA_CHALLENGE, "");
	}
}
