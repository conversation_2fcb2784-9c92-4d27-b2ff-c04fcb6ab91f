@echo off
echo ========================================
echo    IMPORTING GAME DATABASES WITH XAMPP
echo ========================================

cd /d C:\home

echo Setting MySQL path for XAMPP...
set PATH=C:\xampp\mysql\bin;%PATH%

echo Creating databases...

C:\xampp\mysql\bin\mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS h_sanguo_test_user_center CHARACTER SET utf8 COLLATE utf8_general_ci;"
C:\xampp\mysql\bin\mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS h_sanguo_test_game_router CHARACTER SET utf8 COLLATE utf8_general_ci;"
C:\xampp\mysql\bin\mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS h_sanguo_test_game1 CHARACTER SET utf8 COLLATE utf8_general_ci;"
C:\xampp\mysql\bin\mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS h_sanguo_test_game_data CHARACTER SET utf8 COLLATE utf8_general_ci;"
C:\xampp\mysql\bin\mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS h_sanguo_test_game_global_log CHARACTER SET utf8 COLLATE utf8_general_ci;"
C:\xampp\mysql\bin\mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS h_sanguo_test_game_log CHARACTER SET utf8 COLLATE utf8_general_ci;"

echo Databases created successfully!
echo.

echo Importing User Center database...
C:\xampp\mysql\bin\mysql -u root -p123456 h_sanguo_test_user_center < sql\h_sanguo_test_user_center.sql
if %errorlevel% equ 0 (
    echo User Center database imported successfully!
) else (
    echo Error importing User Center database!
    pause
    exit /b 1
)

echo Importing Game Router database...
C:\xampp\mysql\bin\mysql -u root -p123456 h_sanguo_test_game_router < sql\h_sanguo_test_game_router.sql
if %errorlevel% equ 0 (
    echo Game Router database imported successfully!
) else (
    echo Error importing Game Router database!
    pause
    exit /b 1
)

echo Importing Game1 database...
C:\xampp\mysql\bin\mysql -u root -p123456 h_sanguo_test_game1 < sql\h_sanguo_test_game1.sql
if %errorlevel% equ 0 (
    echo Game1 database imported successfully!
) else (
    echo Error importing Game1 database!
    pause
    exit /b 1
)

echo Importing Game Data database...
C:\xampp\mysql\bin\mysql -u root -p123456 h_sanguo_test_game_data < sql\h_sanguo_test_game_data.sql
if %errorlevel% equ 0 (
    echo Game Data database imported successfully!
) else (
    echo Error importing Game Data database!
    pause
    exit /b 1
)

echo Importing Game Global Log database...
C:\xampp\mysql\bin\mysql -u root -p123456 h_sanguo_test_game_global_log < sql\h_sanguo_test_game_global_log.sql
if %errorlevel% equ 0 (
    echo Game Global Log database imported successfully!
) else (
    echo Error importing Game Global Log database!
    pause
    exit /b 1
)

echo Importing Game Log database...
C:\xampp\mysql\bin\mysql -u root -p123456 h_sanguo_test_game_log < sql\h_sanguo_test_game_log.sql
if %errorlevel% equ 0 (
    echo Game Log database imported successfully!
) else (
    echo Error importing Game Log database!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    ALL DATABASES IMPORTED SUCCESSFULLY!
echo ========================================
echo.

echo Verifying databases...
mysql -u root -p123456 -e "SHOW DATABASES LIKE 'h_sanguo_test_%';"

echo.
echo Import completed! Press any key to exit...
pause
