/*
 Navicat Premium Dump SQL

 Source Server         : game
 Source Server Type    : MySQL
 Source Server Version : 50553 (5.5.53)
 Source Host           : localhost:3306
 Source Schema         : h_sanguo_test_game_log

 Target Server Type    : MySQL
 Target Server Version : 50553 (5.5.53)
 File Encoding         : 65001

 Date: 02/03/2025 13:10:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gm_log
-- ----------------------------
DROP TABLE IF EXISTS `gm_log`;
CREATE TABLE `gm_log`  (
  `log_date` datetime NULL DEFAULT NULL,
  `user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `action` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `message` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for log_account_create
-- ----------------------------
DROP TABLE IF EXISTS `log_account_create`;
CREATE TABLE `log_account_create`  (
  `time` datetime NULL DEFAULT NULL,
  `areaId` int(11) NULL DEFAULT NULL,
  `channel_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `account` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `deviceName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `deviceId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `osName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `osVersion` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sdk` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sdkVersion` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `mcc` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  INDEX `time_index`(`time`) USING BTREE,
  INDEX `account_index`(`accountUid`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for log_player_current
-- ----------------------------
DROP TABLE IF EXISTS `log_player_current`;
CREATE TABLE `log_player_current`  (
  `role_id` bigint(20) NOT NULL,
  `device_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `platform_id` int(11) NULL DEFAULT NULL,
  `platform_account` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `account` bigint(20) NULL DEFAULT NULL,
  `role_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_server` smallint(6) UNSIGNED NULL DEFAULT NULL,
  `current_server` smallint(6) UNSIGNED NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `last_login_time` datetime NULL DEFAULT NULL,
  `last_offline_time` datetime NULL DEFAULT NULL,
  `online_long_total` int(11) UNSIGNED NULL DEFAULT NULL,
  `isForbid` tinyint(4) NULL DEFAULT NULL,
  `level` int(11) UNSIGNED NULL DEFAULT NULL,
  `vip_level` int(11) UNSIGNED NULL DEFAULT NULL,
  `profession` int(11) NULL DEFAULT NULL,
  `fight_power` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `exp` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `mission_star_total` int(11) UNSIGNED NULL DEFAULT NULL,
  `recharge_total` int(11) UNSIGNED NULL DEFAULT NULL,
  `gold` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `diamond_recharge` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `diamond_present` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `diamond_total_recharge` bigint(20) NULL DEFAULT NULL,
  `diamond_total_consume` bigint(20) NULL DEFAULT NULL,
  `prestige` int(11) NULL DEFAULT NULL,
  `honour` int(11) NULL DEFAULT NULL,
  `brave` int(11) NULL DEFAULT NULL,
  `qiling` int(11) NULL DEFAULT NULL,
  `spar` int(11) NULL DEFAULT NULL,
  `last_newbie_finish_id` int(11) NULL DEFAULT 0,
  `last_newbie_finish_time` datetime NULL DEFAULT NULL,
  `server_ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_port` int(11) NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  PRIMARY KEY (`role_id`) USING BTREE,
  INDEX `account_index`(`account`) USING BTREE,
  INDEX `rolename_index`(`role_name`) USING BTREE,
  INDEX `createtime_index`(`create_time`) USING BTREE,
  INDEX `last_newbie_finish_id_index`(`last_newbie_finish_id`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_item_log
-- ----------------------------
DROP TABLE IF EXISTS `t_item_log`;
CREATE TABLE `t_item_log`  (
  `areaId` int(20) NULL DEFAULT NULL,
  `channel_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` bigint(20) NULL DEFAULT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `plvl` int(11) NULL DEFAULT NULL,
  `pvip` int(11) UNSIGNED NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `item_id` int(11) NULL DEFAULT NULL,
  `change_num` int(11) NULL DEFAULT NULL,
  `current_num` bigint(20) NULL DEFAULT NULL,
  `log_type` int(11) NULL DEFAULT NULL,
  `log_ext` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  INDEX `userid_index`(`pid`) USING BTREE,
  INDEX `roleid_index`(`areaId`) USING BTREE,
  INDEX `rolename_index`(`plvl`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_action_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_action_log`;
CREATE TABLE `t_player_action_log`  (
  `areaId` int(20) NULL DEFAULT NULL,
  `channel_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` bigint(20) NULL DEFAULT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `plvl` int(11) UNSIGNED NULL DEFAULT NULL,
  `pvip` int(11) NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `log_type` int(11) NULL DEFAULT NULL,
  `log_ext` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  INDEX `userid_index`(`pid`) USING BTREE,
  INDEX `roleid_index`(`areaId`) USING BTREE,
  INDEX `rolename_index`(`pname`) USING BTREE,
  INDEX `time_index`(`log_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_guide_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_guide_log`;
CREATE TABLE `t_player_guide_log`  (
  `areaId` int(20) NULL DEFAULT NULL,
  `channel_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` bigint(20) NULL DEFAULT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `plvl` int(11) UNSIGNED NULL DEFAULT NULL,
  `pvip` int(11) NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `log_type` int(11) NULL DEFAULT NULL,
  `log_ext` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  INDEX `userid_index`(`pid`) USING BTREE,
  INDEX `roleid_index`(`areaId`) USING BTREE,
  INDEX `rolename_index`(`pname`) USING BTREE,
  INDEX `time_index`(`log_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_linenum_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_linenum_log`;
CREATE TABLE `t_player_linenum_log`  (
  `date` datetime NULL DEFAULT NULL,
  `num` int(11) NULL DEFAULT NULL,
  `areaId` int(20) NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  `channel_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for t_player_login_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_login_log`;
CREATE TABLE `t_player_login_log`  (
  `areaId` int(20) NULL DEFAULT NULL,
  `channel_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` bigint(20) NOT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `plvl` int(11) UNSIGNED NULL DEFAULT NULL,
  `pvip` int(11) NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `log_type` int(11) NULL DEFAULT NULL,
  `log_ext` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `imei` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `device_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `os_version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `client_version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  INDEX `userid_index`(`pid`) USING BTREE,
  INDEX `roleid_index`(`areaId`) USING BTREE,
  INDEX `rolename_index`(`pname`) USING BTREE,
  INDEX `time_index`(`log_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_logout_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_logout_log`;
CREATE TABLE `t_player_logout_log`  (
  `areaId` int(20) NULL DEFAULT NULL,
  `channel_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` bigint(20) NULL DEFAULT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `plvl` int(11) UNSIGNED NULL DEFAULT NULL,
  `pvip` int(11) NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `log_type` int(11) NULL DEFAULT NULL,
  `log_ext` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  `onlineTime` int(11) NULL DEFAULT NULL,
  `exp` bigint(20) NULL DEFAULT NULL,
  `diamond` int(11) NULL DEFAULT NULL,
  `power` int(11) NULL DEFAULT NULL,
  `chargediamond` int(11) NULL DEFAULT NULL,
  `costdiamond` int(11) NULL DEFAULT NULL,
  `givediamond` int(11) NULL DEFAULT NULL,
  INDEX `userid_index`(`pid`) USING BTREE,
  INDEX `roleid_index`(`areaId`) USING BTREE,
  INDEX `rolename_index`(`pname`) USING BTREE,
  INDEX `time_index`(`log_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_lvl_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_lvl_log`;
CREATE TABLE `t_player_lvl_log`  (
  `areaId` int(20) NULL DEFAULT NULL,
  `channel_id` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` bigint(20) NULL DEFAULT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `plvl` int(11) UNSIGNED NULL DEFAULT NULL,
  `pvip` int(11) NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `log_type` int(11) NULL DEFAULT NULL,
  `log_ext` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  INDEX `userid_index`(`pid`) USING BTREE,
  INDEX `roleid_index`(`areaId`) USING BTREE,
  INDEX `rolename_index`(`pname`) USING BTREE,
  INDEX `time_index`(`log_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_rank_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_rank_log`;
CREATE TABLE `t_player_rank_log`  (
  `rankType` int(11) NULL DEFAULT NULL,
  `time` varchar(128) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `playerId` bigint(20) NULL DEFAULT NULL,
  `rank` int(11) NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL,
  `serverId` int(11) NULL DEFAULT NULL,
  `session` int(11) NULL DEFAULT NULL,
  `logExt` varchar(255) CHARACTER SET gbk COLLATE gbk_chinese_ci NULL DEFAULT NULL
) ENGINE = MyISAM CHARACTER SET = gbk COLLATE = gbk_chinese_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_player_recharge_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_recharge_log`;
CREATE TABLE `t_player_recharge_log`  (
  `log_date` datetime NOT NULL COMMENT '日志时间',
  `uid` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户ID',
  `playerName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色',
  `vipLevel` int(11) NULL DEFAULT NULL COMMENT 'VIP',
  `player_level` int(11) NULL DEFAULT NULL COMMENT '玩家等级',
  `pid` bigint(20) NULL DEFAULT NULL COMMENT '玩家ID',
  `cp_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品ID',
  `channel_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道ID',
  `server_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务器ID',
  `order_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `money` double NULL DEFAULT NULL COMMENT '金额',
  `type` int(11) NULL DEFAULT NULL COMMENT '充值类型',
  `currency_type` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货币类型(如：USD)',
  `currency_unit` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货币单位(如:元)',
  `item_count` int(11) NULL DEFAULT NULL COMMENT '发放道具数量(一般为钻石)',
  `item_count_before` bigint(20) NULL DEFAULT NULL COMMENT '发放钻石则记录发放前',
  `item_count_after` bigint(20) NULL DEFAULT NULL COMMENT '发放钻石则记录发放后',
  `channel_app_id` int(11) NULL DEFAULT 0,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '玩家名',
  `pvip` int(11) NULL DEFAULT NULL COMMENT '玩家vip等级',
  PRIMARY KEY (`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '充值日志' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for t_player_register_log
-- ----------------------------
DROP TABLE IF EXISTS `t_player_register_log`;
CREATE TABLE `t_player_register_log`  (
  `areaId` int(11) NULL DEFAULT NULL,
  `channel_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `server_id` int(11) NULL DEFAULT NULL,
  `accountId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `accountUid` bigint(20) NULL DEFAULT NULL,
  `pid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pname` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `log_date` datetime NULL DEFAULT NULL,
  `channel_app_id` int(11) NULL DEFAULT 0,
  `imei` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  INDEX `account_index`(`accountUid`) USING BTREE,
  INDEX `rolename_index`(`pname`) USING BTREE,
  INDEX `createtime_index`(`log_date`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
