package base;

import java.util.List;

import Message.S2CIndigoMsg.SingleRankMsg;
import game.core.pub.command.Handler;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
/**
 * 修改玩家名称同步
 * 
 * 更新相关系统的排行榜数据上的玩家名称
 * 
 * <AUTHOR>
 *
 * 2018年10月12日
 */
public class ModifyPlayNameScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs)arg;
		String newName = (String)args.get(Key.ARG1);
		long playerId = (long)args.get(Key.ARG2);
		
		//三国霸主排行榜系统
		IndigoService.getInstance().addCommand(new Handler() {
			@Override
			public void action() {
				indigoRankOperation(newName, playerId);
			}
		});
		//
		
		
		
		return null;
	}
	/**
	 * 三国霸主排行榜
	 * @param newName
	 * @param playerId
	 */
	private void indigoRankOperation(String newName, long playerId) {
		Integer groupId = IndigoService.getInstance().getGroupByPlayerId(playerId);
		if (null == groupId)
			return;
		List<SingleRankMsg> list = IndigoService.getInstance().getRankList().get(groupId);
		if(list == null || list.size() == 0)
			return;
		int index = -1;
		SingleRankMsg newRankMsg = null;
		for (int i = 0; i < list.size(); i++) {
			SingleRankMsg rankMsg = list.get(i);
			if(rankMsg.getPlayerId() == playerId) {
				index = i;
				newRankMsg = rankMsg.toBuilder().setName(newName).build();
				break;
			}
		}
		if(newRankMsg != null && index != -1) {
			list.remove(index);
			list.add(index, newRankMsg);
		}
	}

}
